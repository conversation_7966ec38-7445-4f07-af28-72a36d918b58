import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Video } from '../data/mockData';
import { useVideoPlayer } from '../hooks/useVideoPlayer';
import { useGestures } from '../hooks/useGestures';
import VideoControls from './VideoControls';
import VideoProgress from './VideoProgress';

interface VideoCardProps {
  video: Video;
  isActive: boolean;
  onLike: (videoId: string) => void;
  onComment: (videoId: string) => void;
  onShare: (videoId: string) => void;
  onBookmark?: (videoId: string) => void;
  onUserClick?: (userId: string) => void;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  isActive,
  onLike,
  onComment,
  onShare,
  onBookmark,
  onUserClick
}) => {
  const [showControls, setShowControls] = useState(true);
  const [showProgress, setShowProgress] = useState(false);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);

  const { videoRef, state, actions } = useVideoPlayer(video.videoUrl, isActive);

  // Auto-hide controls
  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    setShowControls(true);
    const timeout = setTimeout(() => {
      setShowControls(false);
    }, 3000);
    setControlsTimeout(timeout);
  }, [controlsTimeout]);

  // Gesture handlers
  const gestureRef = useGestures({
    onTap: () => {
      actions.togglePlay();
      resetControlsTimeout();
    },
    onDoubleTap: () => {
      onLike(video.id);
    },
    onLongPress: () => {
      setShowProgress(true);
      setTimeout(() => setShowProgress(false), 2000);
    }
  });

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div
      ref={gestureRef}
      className="relative w-full h-screen bg-black overflow-hidden"
      onMouseEnter={resetControlsTimeout}
      onMouseMove={resetControlsTimeout}
    >
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        loop
        muted
        playsInline
        poster={video.thumbnail}
      >
        <source src={video.videoUrl} type="video/mp4" />
      </video>

      {/* Loading Spinner */}
      <AnimatePresence>
        {state.isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50"
          >
            <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Play/Pause Overlay */}
      <AnimatePresence>
        {!state.isPlaying && !state.isLoading && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30"
          >
            <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <i className="bi bi-play-fill text-white text-3xl ml-1"></i>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Double Tap Like Animation */}
      <AnimatePresence>
        {video.isLiked && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.3 }}
            className="absolute inset-0 flex items-center justify-center pointer-events-none"
          >
            <motion.i
              className="bi bi-heart-fill text-red-500 text-8xl"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: 1 }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Video Progress */}
      <AnimatePresence>
        {showProgress && (
          <VideoProgress
            currentTime={state.currentTime}
            duration={state.duration}
            onSeek={actions.seekTo}
          />
        )}
      </AnimatePresence>

      {/* Video Controls */}
      <AnimatePresence>
        {showControls && (
          <VideoControls
            isPlaying={state.isPlaying}
            isMuted={state.isMuted}
            volume={state.volume}
            onPlayPause={actions.togglePlay}
            onMute={actions.toggleMute}
            onVolumeChange={actions.setVolume}
            onFullscreen={actions.toggleFullscreen}
          />
        )}
      </AnimatePresence>

      {/* User Info and Caption */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: showControls ? 1 : 0.7, y: 0 }}
        className="absolute bottom-32 left-4 right-20 text-white"
      >
        <div className="flex items-center mb-3">
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => onUserClick?.(video.user.id)}
            className="flex items-center"
          >
            <img
              src={video.user.avatar}
              alt={video.user.displayName}
              className="w-12 h-12 rounded-full border-2 border-white mr-3"
              crossOrigin="anonymous"
            />
            <div>
              <div className="flex items-center">
                <span className="font-semibold text-lg">{video.user.username}</span>
                {video.user.verified && (
                  <i className="bi bi-patch-check-fill text-blue-400 ml-1"></i>
                )}
              </div>
              <span className="text-sm text-gray-300">{video.user.displayName}</span>
            </div>
          </motion.button>

          {/* Follow Button */}
          {!video.user.isFollowing && (
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="ml-4 px-4 py-1 bg-primary-500 text-white text-sm font-semibold rounded-full hover:bg-primary-600 transition-colors"
            >
              Follow
            </motion.button>
          )}
        </div>

        <p className="text-sm mb-2 leading-relaxed">{video.caption}</p>

        {/* Hashtags */}
        <div className="flex flex-wrap gap-1 mb-2">
          {video.hashtags.map((hashtag, index) => (
            <span key={index} className="text-xs text-blue-400 hover:text-blue-300 cursor-pointer">
              {hashtag}
            </span>
          ))}
        </div>

        <div className="flex items-center justify-between text-xs text-gray-300">
          <div className="flex items-center">
            <i className="bi bi-music-note mr-1"></i>
            <span>{video.music}</span>
          </div>
          <div className="flex items-center space-x-3">
            <span>{formatNumber(video.views)} views</span>
            <span>{formatTime(video.duration)}</span>
          </div>
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: showControls ? 1 : 0.7, x: 0 }}
        className="absolute bottom-32 right-4 flex flex-col items-center space-y-6"
      >
        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => onLike(video.id)}
          className="flex flex-col items-center group"
        >
          <motion.div
            className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${
              video.isLiked ? 'bg-primary-500 scale-110' : 'bg-gray-800 bg-opacity-70 group-hover:bg-opacity-90'
            }`}
            animate={video.isLiked ? { scale: [1, 1.2, 1] } : {}}
            transition={{ duration: 0.3 }}
          >
            <i className={`bi ${video.isLiked ? 'bi-heart-fill' : 'bi-heart'} text-xl text-white`}></i>
          </motion.div>
          <span className="text-xs text-white mt-1">{formatNumber(video.likes)}</span>
        </motion.button>

        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => onComment(video.id)}
          className="flex flex-col items-center group"
        >
          <div className="w-12 h-12 bg-gray-800 bg-opacity-70 rounded-full flex items-center justify-center group-hover:bg-opacity-90 transition-all duration-200">
            <i className="bi bi-chat text-xl text-white"></i>
          </div>
          <span className="text-xs text-white mt-1">{formatNumber(video.comments)}</span>
        </motion.button>

        {/* Bookmark Button */}
        {onBookmark && (
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={() => onBookmark(video.id)}
            className="flex flex-col items-center group"
          >
            <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${
              video.isBookmarked ? 'bg-yellow-500' : 'bg-gray-800 bg-opacity-70 group-hover:bg-opacity-90'
            }`}>
              <i className={`bi ${video.isBookmarked ? 'bi-bookmark-fill' : 'bi-bookmark'} text-xl text-white`}></i>
            </div>
          </motion.button>
        )}

        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => onShare(video.id)}
          className="flex flex-col items-center group"
        >
          <div className="w-12 h-12 bg-gray-800 bg-opacity-70 rounded-full flex items-center justify-center group-hover:bg-opacity-90 transition-all duration-200">
            <i className="bi bi-share text-xl text-white"></i>
          </div>
          <span className="text-xs text-white mt-1">{formatNumber(video.shares)}</span>
        </motion.button>

        {/* Rotating Music Disc */}
        <motion.div
          animate={{ rotate: state.isPlaying ? 360 : 0 }}
          transition={{
            duration: 3,
            repeat: state.isPlaying ? Infinity : 0,
            ease: "linear"
          }}
          className="w-12 h-12 rounded-full border-2 border-white overflow-hidden bg-gray-800"
        >
          <img
            src={video.user.avatar}
            alt="Music"
            className="w-full h-full object-cover"
            crossOrigin="anonymous"
          />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default VideoCard;
