import React from 'react';
import { motion } from 'framer-motion';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'white' | 'gray';
  text?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  text,
  fullScreen = false
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const colorClasses = {
    primary: 'border-primary-500',
    white: 'border-white',
    gray: 'border-gray-400'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const spinner = (
    <div className="flex flex-col items-center justify-center space-y-4">
      {/* Main Spinner */}
      <motion.div
        className={`${sizeClasses[size]} border-4 ${colorClasses[color]} border-t-transparent rounded-full`}
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      />
      
      {/* Pulsing Dots */}
      <div className="flex space-x-1">
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className={`w-2 h-2 rounded-full ${
              color === 'primary' ? 'bg-primary-500' : 
              color === 'white' ? 'bg-white' : 'bg-gray-400'
            }`}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2
            }}
          />
        ))}
      </div>

      {/* Loading Text */}
      {text && (
        <motion.p
          className={`${textSizeClasses[size]} ${
            color === 'primary' ? 'text-primary-500' : 
            color === 'white' ? 'text-white' : 'text-gray-400'
          } font-medium`}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div className="bg-gray-900 rounded-lg p-8">
          {spinner}
        </div>
      </motion.div>
    );
  }

  return spinner;
};

// Skeleton Loading Component
interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  variant = 'text',
  width,
  height,
  animation = 'pulse'
}) => {
  const baseClasses = 'bg-gray-700';
  
  const variantClasses = {
    text: 'rounded',
    rectangular: 'rounded-lg',
    circular: 'rounded-full'
  };

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'shimmer'
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${animationClasses[animation]} ${className}`}
      style={style}
    />
  );
};

// Video Loading Skeleton
export const VideoSkeleton: React.FC = () => (
  <div className="w-full h-screen bg-gray-900 relative overflow-hidden">
    <Skeleton className="w-full h-full" variant="rectangular" />
    
    {/* User info skeleton */}
    <div className="absolute bottom-32 left-4 right-20">
      <div className="flex items-center mb-3">
        <Skeleton variant="circular" width={48} height={48} className="mr-3" />
        <div className="flex-1">
          <Skeleton width="120px" height="20px" className="mb-1" />
          <Skeleton width="80px" height="16px" />
        </div>
      </div>
      <Skeleton width="90%" height="16px" className="mb-2" />
      <Skeleton width="60%" height="14px" />
    </div>

    {/* Action buttons skeleton */}
    <div className="absolute bottom-32 right-4 flex flex-col items-center space-y-6">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="flex flex-col items-center">
          <Skeleton variant="circular" width={48} height={48} />
          <Skeleton width="24px" height="12px" className="mt-1" />
        </div>
      ))}
    </div>
  </div>
);

export default LoadingSpinner;
