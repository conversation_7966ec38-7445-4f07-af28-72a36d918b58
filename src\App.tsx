import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import VideoFeed from './components/VideoFeed';
import BottomNavigation from './components/BottomNavigation';
import TopNavigation from './components/TopNavigation';
import UserProfile from './components/UserProfile';
import UploadInterface from './components/UploadInterface';
import SearchInterface from './components/SearchInterface';
import { mockUsers } from './data/mockData';
import { useLocalStorage } from './hooks/useLocalStorage';

function App() {
  const [activeTab, setActiveTab] = useState('home');
  const [showProfile, setShowProfile] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedUser, setSelectedUser] = useState(mockUsers[0]);
  const [currentUser] = useLocalStorage('currentUser', mockUsers[0]);
  const [theme, setTheme] = useLocalStorage('theme', 'dark');

  // Apply theme to document
  useEffect(() => {
    document.documentElement.className = theme;
  }, [theme]);

  const handleTabChange = (tab: string) => {
    if (tab === 'upload') {
      setShowUpload(true);
    } else if (tab === 'profile') {
      setSelectedUser(currentUser);
      setShowProfile(true);
    } else {
      setActiveTab(tab);
      // Close any open modals when switching tabs
      setShowProfile(false);
      setShowUpload(false);
      setShowSearch(false);
    }
  };

  const handleUserSelect = (user: any) => {
    setSelectedUser(user);
    setShowSearch(false);
    setShowProfile(true);
  };

  const handleVideoSelect = (videoId: string) => {
    // Navigate to specific video
    console.log('Navigate to video:', videoId);
    setShowSearch(false);
  };

  const handleMusicSelect = (musicId: string) => {
    // Navigate to music page or start creating video with this music
    console.log('Selected music:', musicId);
    setShowSearch(false);
    setShowUpload(true);
  };

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">
      {/* Main Content */}
      {activeTab === 'home' && (
        <>
          <TopNavigation onSearchClick={() => setShowSearch(true)} />
          <VideoFeed />
        </>
      )}

      {activeTab === 'discover' && (
        <div className="flex items-center justify-center h-full text-white">
          <div className="text-center">
            <i className="bi bi-compass text-6xl mb-4 text-gray-400"></i>
            <h2 className="text-2xl font-bold mb-2">Discover</h2>
            <p className="text-gray-400">Explore trending content</p>
          </div>
        </div>
      )}

      {activeTab === 'inbox' && (
        <div className="flex items-center justify-center h-full text-white">
          <div className="text-center">
            <i className="bi bi-chat-dots text-6xl mb-4 text-gray-400"></i>
            <h2 className="text-2xl font-bold mb-2">Messages</h2>
            <p className="text-gray-400">Connect with friends</p>
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <BottomNavigation activeTab={activeTab} onTabChange={handleTabChange} />

      {/* Overlays */}
      <AnimatePresence>
        {showProfile && (
          <UserProfile
            user={selectedUser}
            onClose={() => setShowProfile(false)}
            onVideoSelect={handleVideoSelect}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showUpload && (
          <UploadInterface onClose={() => setShowUpload(false)} />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showSearch && (
          <SearchInterface
            onClose={() => setShowSearch(false)}
            onUserSelect={handleUserSelect}
            onVideoSelect={handleVideoSelect}
            onMusicSelect={handleMusicSelect}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

export default App;
