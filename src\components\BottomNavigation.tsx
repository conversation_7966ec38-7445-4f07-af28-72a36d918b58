import React from 'react';
import { motion } from 'framer-motion';

interface BottomNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'home', icon: 'bi-house-fill', label: 'Home' },
    { id: 'discover', icon: 'bi-compass', label: 'Discover' },
    { id: 'upload', icon: 'bi-plus-square', label: 'Upload', isSpecial: true },
    { id: 'inbox', icon: 'bi-chat-dots', label: 'Inbox' },
    { id: 'profile', icon: 'bi-person-circle', label: 'Profile' }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-black bg-opacity-90 backdrop-blur-sm border-t border-gray-800 z-40">
      <div className="flex items-center justify-around py-2">
        {tabs.map((tab) => (
          <motion.button
            key={tab.id}
            whileTap={{ scale: 0.9 }}
            onClick={() => onTabChange(tab.id)}
            className={`flex flex-col items-center py-2 px-3 ${
              tab.isSpecial ? 'relative' : ''
            }`}
          >
            {tab.isSpecial ? (
              <div className="w-12 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                <i className={`${tab.icon} text-white text-lg`}></i>
              </div>
            ) : (
              <i className={`${tab.icon} text-xl ${
                activeTab === tab.id ? 'text-white' : 'text-gray-400'
              }`}></i>
            )}
            <span className={`text-xs mt-1 ${
              activeTab === tab.id ? 'text-white' : 'text-gray-400'
            }`}>
              {tab.label}
            </span>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export default BottomNavigation;
