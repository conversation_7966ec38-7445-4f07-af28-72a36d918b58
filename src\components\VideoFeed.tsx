import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import VideoCard from './VideoCard';
import { mockVideos, Video } from '../data/mockData';

const VideoFeed: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [videos, setVideos] = useState<Video[]>(mockVideos);
  const [isScrolling, setIsScrolling] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef<number>(0);
  const touchEndY = useRef<number>(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartY.current = e.targetTouches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndY.current = e.targetTouches[0].clientY;
  };

  const handleTouchEnd = () => {
    if (!isScrolling) {
      const deltaY = touchStartY.current - touchEndY.current;
      const threshold = 50;

      if (Math.abs(deltaY) > threshold) {
        setIsScrolling(true);
        if (deltaY > 0 && currentIndex < videos.length - 1) {
          setCurrentIndex(currentIndex + 1);
        } else if (deltaY < 0 && currentIndex > 0) {
          setCurrentIndex(currentIndex - 1);
        }
        window.setTimeout(() => setIsScrolling(false), 500);
      }
    }
  };

  const handleWheel = (e: WheelEvent) => {
    e.preventDefault();
    if (!isScrolling) {
      setIsScrolling(true);
      if (e.deltaY > 0 && currentIndex < videos.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else if (e.deltaY < 0 && currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
      window.setTimeout(() => setIsScrolling(false), 500);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
      return () => container.removeEventListener('wheel', handleWheel);
    }
  }, [currentIndex, isScrolling]);

  const handleLike = (videoId: string) => {
    setVideos(videos.map(video => 
      video.id === videoId 
        ? { 
            ...video, 
            isLiked: !video.isLiked,
            likes: video.isLiked ? video.likes - 1 : video.likes + 1
          }
        : video
    ));
  };

  const handleComment = (videoId: string) => {
    console.log('Comment on video:', videoId);
  };

  const handleShare = (videoId: string) => {
    console.log('Share video:', videoId);
  };

  return (
    <div
      ref={containerRef}
      className="relative w-full h-screen overflow-hidden bg-black"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ y: '100%' }}
          animate={{ y: 0 }}
          exit={{ y: '-100%' }}
          transition={{ type: 'tween', duration: 0.3 }}
          className="absolute inset-0"
        >
          <VideoCard
            video={videos[currentIndex]}
            isActive={true}
            onLike={handleLike}
            onComment={handleComment}
            onShare={handleShare}
          />
        </motion.div>
      </AnimatePresence>

      {/* Video Progress Indicators */}
      <div className="absolute top-4 right-4 flex flex-col space-y-1 z-10">
        {videos.map((_, index) => (
          <div
            key={index}
            className={`w-1 h-8 rounded-full transition-all duration-300 ${
              index === currentIndex ? 'bg-white' : 'bg-gray-500'
            }`}
          />
        ))}
      </div>

      {/* Swipe Hint */}
      {currentIndex === 0 && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
          transition={{ delay: 3, duration: 1 }}
          className="absolute bottom-32 left-1/2 transform -translate-x-1/2 text-white text-center z-10"
        >
          <motion.div
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <i className="bi bi-chevron-up text-2xl"></i>
          </motion.div>
          <p className="text-sm mt-1">Swipe up for more</p>
        </motion.div>
      )}
    </div>
  );
};

export default VideoFeed;
