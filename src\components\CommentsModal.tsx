import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Comment, Video } from '../data/mockData';

interface CommentsModalProps {
  video: Video;
  isOpen: boolean;
  onClose: () => void;
  onAddComment: (videoId: string, text: string) => void;
  onLikeComment: (commentId: string) => void;
}

const CommentsModal: React.FC<CommentsModalProps> = ({
  video,
  isOpen,
  onClose,
  onAddComment,
  onLikeComment
}) => {
  const [newComment, setNewComment] = useState('');
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const commentsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      onAddComment(video.id, newComment.trim());
      setNewComment('');
      setReplyTo(null);
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const CommentItem: React.FC<{ comment: Comment; isReply?: boolean }> = ({ comment, isReply = false }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex space-x-3 ${isReply ? 'ml-12 mt-2' : 'mb-4'}`}
    >
      <img
        src={comment.user.avatar}
        alt={comment.user.displayName}
        className="w-8 h-8 rounded-full flex-shrink-0"
        crossOrigin="anonymous"
      />
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2 mb-1">
          <span className="font-semibold text-white text-sm">{comment.user.username}</span>
          {comment.user.verified && (
            <i className="bi bi-patch-check-fill text-blue-400 text-xs"></i>
          )}
          <span className="text-gray-400 text-xs">{formatTimeAgo(comment.createdAt)}</span>
        </div>
        <p className="text-white text-sm leading-relaxed mb-2">{comment.text}</p>
        <div className="flex items-center space-x-4 text-xs">
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => onLikeComment(comment.id)}
            className={`flex items-center space-x-1 ${
              comment.isLiked ? 'text-red-400' : 'text-gray-400'
            } hover:text-red-300 transition-colors`}
          >
            <i className={`bi ${comment.isLiked ? 'bi-heart-fill' : 'bi-heart'}`}></i>
            <span>{formatNumber(comment.likes)}</span>
          </motion.button>
          {!isReply && (
            <button
              onClick={() => setReplyTo(comment.id)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              Reply
            </button>
          )}
        </div>
        
        {/* Replies */}
        {comment.replies && comment.replies.length > 0 && (
          <div className="mt-3">
            {comment.replies.map((reply) => (
              <CommentItem key={reply.id} comment={reply} isReply />
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end"
      onClick={onClose}
    >
      <motion.div
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ type: 'spring', damping: 25, stiffness: 500 }}
        className="w-full bg-gray-900 rounded-t-2xl max-h-[80vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h3 className="text-white font-semibold text-lg">
            {formatNumber(video.comments)} comments
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <i className="bi bi-x text-2xl"></i>
          </button>
        </div>

        {/* Comments List */}
        <div
          ref={commentsRef}
          className="flex-1 overflow-y-auto p-4 space-y-4"
        >
          {video.commentsData && video.commentsData.length > 0 ? (
            video.commentsData.map((comment) => (
              <CommentItem key={comment.id} comment={comment} />
            ))
          ) : (
            <div className="text-center text-gray-400 py-8">
              <i className="bi bi-chat text-4xl mb-2 block"></i>
              <p>No comments yet</p>
              <p className="text-sm">Be the first to comment!</p>
            </div>
          )}
        </div>

        {/* Comment Input */}
        <div className="p-4 border-t border-gray-700">
          {replyTo && (
            <div className="flex items-center justify-between mb-2 p-2 bg-gray-800 rounded-lg">
              <span className="text-sm text-gray-300">Replying to comment</span>
              <button
                onClick={() => setReplyTo(null)}
                className="text-gray-400 hover:text-white"
              >
                <i className="bi bi-x"></i>
              </button>
            </div>
          )}
          <form onSubmit={handleSubmit} className="flex space-x-3">
            <div className="flex-1 relative">
              <input
                ref={inputRef}
                type="text"
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full bg-gray-800 text-white rounded-full px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-primary-500"
                maxLength={500}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                {newComment.length}/500
              </div>
            </div>
            <motion.button
              whileTap={{ scale: 0.95 }}
              type="submit"
              disabled={!newComment.trim()}
              className={`px-6 py-2 rounded-full font-semibold transition-colors ${
                newComment.trim()
                  ? 'bg-primary-500 text-white hover:bg-primary-600'
                  : 'bg-gray-700 text-gray-400 cursor-not-allowed'
              }`}
            >
              Post
            </motion.button>
          </form>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default CommentsModal;
