import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface UploadInterfaceProps {
  onClose: () => void;
}

const UploadInterface: React.FC<UploadInterfaceProps> = ({ onClose }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [caption, setCaption] = useState('');
  const [selectedMusic, setSelectedMusic] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const musicOptions = [
    'Chill Vibes - Lo-fi Beats',
    'Pump It Up - Workout Mix',
    'Kitchen Vibes - Upbeat',
    'Tropical Dreams - Ambient',
    'City Nights - Electronic',
    'Acoustic Sunset - Guitar'
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('video/')) {
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const interval = window.setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          window.clearInterval(interval);
          setIsUploading(false);
          onClose();
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black z-50 overflow-y-auto"
    >
      {/* Header */}
      <div className="sticky top-0 bg-black bg-opacity-90 backdrop-blur-sm z-10 px-4 py-3 flex items-center justify-between">
        <button onClick={onClose} className="text-white">
          <i className="bi bi-x text-2xl"></i>
        </button>
        <span className="text-white font-semibold">Create Video</span>
        <button
          onClick={handleUpload}
          disabled={!selectedFile || isUploading}
          className={`px-4 py-2 rounded-lg font-semibold transition-colors ${
            selectedFile && !isUploading
              ? 'bg-primary-500 text-white'
              : 'bg-gray-700 text-gray-400'
          }`}
        >
          {isUploading ? 'Uploading...' : 'Post'}
        </button>
      </div>

      <div className="px-4 py-6">
        {/* Upload Area */}
        <div className="mb-6">
          {!selectedFile ? (
            <motion.div
              whileTap={{ scale: 0.98 }}
              onClick={() => fileInputRef.current?.click()}
              className="border-2 border-dashed border-gray-600 rounded-lg p-12 text-center cursor-pointer hover:border-primary-500 transition-colors"
            >
              <i className="bi bi-cloud-upload text-4xl text-gray-400 mb-4 block"></i>
              <p className="text-white text-lg mb-2">Upload your video</p>
              <p className="text-gray-400 text-sm">Tap to select a video from your device</p>
            </motion.div>
          ) : (
            <div className="relative bg-gray-900 rounded-lg overflow-hidden">
              <video
                src={URL.createObjectURL(selectedFile)}
                className="w-full h-64 object-cover"
                controls
              />
              <button
                onClick={() => setSelectedFile(null)}
                className="absolute top-2 right-2 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white"
              >
                <i className="bi bi-x"></i>
              </button>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>

        {/* Caption */}
        <div className="mb-6">
          <label className="block text-white font-semibold mb-2">Caption</label>
          <textarea
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            placeholder="Write a caption..."
            className="w-full bg-gray-900 text-white rounded-lg p-3 resize-none focus:outline-none focus:ring-2 focus:ring-primary-500"
            rows={3}
            maxLength={150}
          />
          <div className="text-right text-gray-400 text-sm mt-1">
            {caption.length}/150
          </div>
        </div>

        {/* Music Selection */}
        <div className="mb-6">
          <label className="block text-white font-semibold mb-2">
            <i className="bi bi-music-note mr-2"></i>
            Add Music
          </label>
          <div className="space-y-2">
            {musicOptions.map((music) => (
              <motion.button
                key={music}
                whileTap={{ scale: 0.98 }}
                onClick={() => setSelectedMusic(music)}
                className={`w-full text-left p-3 rounded-lg transition-colors ${
                  selectedMusic === music
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span>{music}</span>
                  {selectedMusic === music && (
                    <i className="bi bi-check-circle-fill"></i>
                  )}
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Editing Options */}
        <div className="mb-6">
          <label className="block text-white font-semibold mb-2">
            <i className="bi bi-scissors mr-2"></i>
            Edit Video
          </label>
          <div className="grid grid-cols-2 gap-3">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="bg-gray-900 text-white p-3 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <i className="bi bi-crop block text-xl mb-1"></i>
              <span className="text-sm">Trim</span>
            </motion.button>
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="bg-gray-900 text-white p-3 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <i className="bi bi-palette block text-xl mb-1"></i>
              <span className="text-sm">Filters</span>
            </motion.button>
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="bg-gray-900 text-white p-3 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <i className="bi bi-speedometer2 block text-xl mb-1"></i>
              <span className="text-sm">Speed</span>
            </motion.button>
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="bg-gray-900 text-white p-3 rounded-lg hover:bg-gray-800 transition-colors"
            >
              <i className="bi bi-type block text-xl mb-1"></i>
              <span className="text-sm">Text</span>
            </motion.button>
          </div>
        </div>

        {/* Upload Progress */}
        <AnimatePresence>
          {isUploading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-gray-900 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-semibold">Uploading...</span>
                <span className="text-gray-400">{uploadProgress}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${uploadProgress}%` }}
                  className="bg-primary-500 h-2 rounded-full"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default UploadInterface;
