import { useState, useRef, useEffect, useCallback } from 'react';

export interface VideoPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useVideoPlayer = (videoUrl: string, isActive: boolean = false) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [state, setState] = useState<VideoPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: true, // Default muted for autoplay
    isFullscreen: false,
    isLoading: true,
    error: null
  });

  // Play/Pause toggle
  const togglePlay = useCallback(() => {
    if (!videoRef.current) return;

    if (state.isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play().catch((error) => {
        setState(prev => ({ ...prev, error: error.message }));
      });
    }
  }, [state.isPlaying]);

  // Seek to specific time
  const seekTo = useCallback((time: number) => {
    if (!videoRef.current) return;
    videoRef.current.currentTime = time;
  }, []);

  // Set volume
  const setVolume = useCallback((volume: number) => {
    if (!videoRef.current) return;
    const clampedVolume = Math.max(0, Math.min(1, volume));
    videoRef.current.volume = clampedVolume;
    setState(prev => ({ ...prev, volume: clampedVolume }));
  }, []);

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!videoRef.current) return;
    const newMutedState = !state.isMuted;
    videoRef.current.muted = newMutedState;
    setState(prev => ({ ...prev, isMuted: newMutedState }));
  }, [state.isMuted]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!videoRef.current) return;

    if (!state.isFullscreen) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [state.isFullscreen]);

  // Handle video events
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadStart = () => setState(prev => ({ ...prev, isLoading: true, error: null }));
    const handleLoadedData = () => setState(prev => ({ ...prev, isLoading: false, duration: video.duration }));
    const handlePlay = () => setState(prev => ({ ...prev, isPlaying: true }));
    const handlePause = () => setState(prev => ({ ...prev, isPlaying: false }));
    const handleTimeUpdate = () => setState(prev => ({ ...prev, currentTime: video.currentTime }));
    const handleVolumeChange = () => setState(prev => ({ ...prev, volume: video.volume, isMuted: video.muted }));
    const handleError = () => setState(prev => ({ ...prev, error: 'Failed to load video', isLoading: false }));
    const handleFullscreenChange = () => {
      setState(prev => ({ ...prev, isFullscreen: !!document.fullscreenElement }));
    };

    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('volumechange', handleVolumeChange);
    video.addEventListener('error', handleError);
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('volumechange', handleVolumeChange);
      video.removeEventListener('error', handleError);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Auto play/pause based on isActive
  useEffect(() => {
    if (!videoRef.current) return;

    if (isActive && !state.isPlaying && !state.error) {
      videoRef.current.play().catch((error) => {
        setState(prev => ({ ...prev, error: error.message }));
      });
    } else if (!isActive && state.isPlaying) {
      videoRef.current.pause();
    }
  }, [isActive, state.isPlaying, state.error]);

  return {
    videoRef,
    state,
    actions: {
      togglePlay,
      seekTo,
      setVolume,
      toggleMute,
      toggleFullscreen
    }
  };
};
