import React from 'react';
import { motion } from 'framer-motion';

interface TopNavigationProps {
  onSearchClick: () => void;
}

const TopNavigation: React.FC<TopNavigationProps> = ({ onSearchClick }) => {
  return (
    <div className="fixed top-0 left-0 right-0 bg-black bg-opacity-50 backdrop-blur-sm z-40 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <motion.button
            whileTap={{ scale: 0.9 }}
            className="text-gray-300 hover:text-white transition-colors"
          >
            <i className="bi bi-broadcast text-xl"></i>
          </motion.button>
          <h1 className="text-white text-xl font-bold">VibeShare</h1>
        </div>

        <div className="flex items-center space-x-4">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={onSearchClick}
            className="text-gray-300 hover:text-white transition-colors"
          >
            <i className="bi bi-search text-xl"></i>
          </motion.button>
          <motion.button
            whileTap={{ scale: 0.9 }}
            className="text-gray-300 hover:text-white transition-colors relative"
          >
            <i className="bi bi-bell text-xl"></i>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full"></div>
          </motion.button>
        </div>
      </div>

      {/* For You / Following Tabs */}
      <div className="flex items-center justify-center mt-4 space-x-8">
        <motion.button
          whileTap={{ scale: 0.95 }}
          className="text-white font-semibold border-b-2 border-white pb-1"
        >
          For You
        </motion.button>
        <motion.button
          whileTap={{ scale: 0.95 }}
          className="text-gray-400 font-semibold pb-1"
        >
          Following
        </motion.button>
      </div>
    </div>
  );
};

export default TopNavigation;
