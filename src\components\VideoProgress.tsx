import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';

interface VideoProgressProps {
  currentTime: number;
  duration: number;
  onSeek: (time: number) => void;
}

const VideoProgress: React.FC<VideoProgressProps> = ({
  currentTime,
  duration,
  onSeek
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragTime, setDragTime] = useState(0);
  const progressRef = useRef<HTMLDivElement>(null);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    handleSeek(e);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      handleSeek(e);
    }
  };

  const handleMouseUp = () => {
    if (isDragging) {
      onSeek(dragTime);
      setIsDragging(false);
    }
  };

  const handleSeek = (e: React.MouseEvent) => {
    if (!progressRef.current) return;
    
    const rect = progressRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));
    const time = percentage * duration;
    
    if (isDragging) {
      setDragTime(time);
    } else {
      onSeek(time);
    }
  };

  const progress = duration > 0 ? (isDragging ? dragTime : currentTime) / duration : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="absolute bottom-16 left-4 right-4 bg-black bg-opacity-50 rounded-lg p-4 backdrop-blur-sm"
    >
      {/* Time Display */}
      <div className="flex justify-between text-white text-sm mb-2">
        <span>{formatTime(isDragging ? dragTime : currentTime)}</span>
        <span>{formatTime(duration)}</span>
      </div>

      {/* Progress Bar */}
      <div
        ref={progressRef}
        className="relative h-2 bg-gray-600 rounded-full cursor-pointer"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Progress Fill */}
        <div
          className="absolute top-0 left-0 h-full bg-white rounded-full transition-all duration-100"
          style={{ width: `${progress * 100}%` }}
        />
        
        {/* Progress Thumb */}
        <div
          className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg transition-all duration-100"
          style={{ left: `calc(${progress * 100}% - 8px)` }}
        />
      </div>

      {/* Chapters/Markers (if available) */}
      <div className="flex justify-between mt-2 text-xs text-gray-400">
        <span>0:00</span>
        <span className="opacity-50">•</span>
        <span>{formatTime(duration / 2)}</span>
        <span className="opacity-50">•</span>
        <span>{formatTime(duration)}</span>
      </div>
    </motion.div>
  );
};

export default VideoProgress;
