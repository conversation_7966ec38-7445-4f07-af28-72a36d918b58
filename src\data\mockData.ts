export interface User {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  bio: string;
  followers: number;
  following: number;
  verified: boolean;
}

export interface Video {
  id: string;
  user: User;
  videoUrl: string;
  thumbnail: string;
  caption: string;
  likes: number;
  comments: number;
  shares: number;
  music: string;
  duration: number;
  isLiked: boolean;
}

export const mockUsers: User[] = [
  {
    id: '1',
    username: 'sarah_creates',
    displayName: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Digital artist & content creator ✨ Sharing daily inspiration',
    followers: 125000,
    following: 892,
    verified: true
  },
  {
    id: '2',
    username: 'mike_fitness',
    displayName: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Fitness coach 💪 Transform your life one workout at a time',
    followers: 89000,
    following: 456,
    verified: false
  },
  {
    id: '3',
    username: 'chef_emma',
    displayName: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Professional chef 👩‍🍳 Quick recipes for busy people',
    followers: 234000,
    following: 123,
    verified: true
  },
  {
    id: '4',
    username: 'travel_alex',
    displayName: 'Alex Thompson',
    avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'World traveler 🌍 Sharing hidden gems from around the globe',
    followers: 67000,
    following: 789,
    verified: false
  }
];

export const mockVideos: Video[] = [
  {
    id: '1',
    user: mockUsers[0],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnail: 'https://images.pexels.com/photos/1181673/pexels-photo-1181673.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: 'Creating digital art magic ✨ What do you think of this piece? #digitalart #creative',
    likes: 12500,
    comments: 892,
    shares: 234,
    music: 'Chill Vibes - Lo-fi Beats',
    duration: 15,
    isLiked: false
  },
  {
    id: '2',
    user: mockUsers[1],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    thumbnail: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: '30-second morning workout routine 💪 Try this every day for amazing results! #fitness #workout',
    likes: 8900,
    comments: 456,
    shares: 123,
    music: 'Pump It Up - Workout Mix',
    duration: 30,
    isLiked: true
  },
  {
    id: '3',
    user: mockUsers[2],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    thumbnail: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: '5-minute pasta recipe that will blow your mind 🍝 Perfect for busy weeknights! #cooking #recipe',
    likes: 15600,
    comments: 1200,
    shares: 567,
    music: 'Kitchen Vibes - Upbeat',
    duration: 45,
    isLiked: false
  },
  {
    id: '4',
    user: mockUsers[3],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    thumbnail: 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: 'Hidden waterfall in Bali 🌊 This place is absolutely magical! Who wants the location? #travel #bali',
    likes: 23400,
    comments: 2100,
    shares: 890,
    music: 'Tropical Dreams - Ambient',
    duration: 25,
    isLiked: true
  }
];
