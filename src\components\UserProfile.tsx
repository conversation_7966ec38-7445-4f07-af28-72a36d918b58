import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, mockVideos } from '../data/mockData';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface UserProfileProps {
  user: User;
  onClose: () => void;
  onVideoSelect?: (videoId: string) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onClose, onVideoSelect }) => {
  const [activeTab, setActiveTab] = useState<'videos' | 'liked' | 'bookmarks'>('videos');
  const [isFollowing, setIsFollowing] = useState(user.isFollowing || false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [followersCount, setFollowersCount] = useState(user.followers);

  // Persistent storage for following status
  const [followingUsers, setFollowingUsers] = useLocalStorage<string[]>('followingUsers', []);

  const userVideos = mockVideos.filter(video => video.user.id === user.id);
  const likedVideos = mockVideos.filter(video => video.isLiked);
  const bookmarkedVideos = mockVideos.filter(video => video.isBookmarked);

  // Initialize following status from localStorage
  useEffect(() => {
    setIsFollowing(followingUsers.includes(user.id));
  }, [followingUsers, user.id]);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const handleFollow = () => {
    const newFollowingState = !isFollowing;
    setIsFollowing(newFollowingState);

    if (newFollowingState) {
      setFollowingUsers(prev => [...prev, user.id]);
      setFollowersCount(prev => prev + 1);
    } else {
      setFollowingUsers(prev => prev.filter(id => id !== user.id));
      setFollowersCount(prev => prev - 1);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Check out ${user.displayName} on VibeShare`,
          text: user.bio,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      setShowShareModal(true);
    }
  };

  const copyProfileLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setShowShareModal(false);
      // Show toast notification
    } catch (error) {
      console.log('Error copying to clipboard:', error);
    }
  };

  const getTabContent = () => {
    switch (activeTab) {
      case 'videos':
        return userVideos;
      case 'liked':
        return likedVideos;
      case 'bookmarks':
        return bookmarkedVideos;
      default:
        return userVideos;
    }
  };

  const tabContent = getTabContent();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black z-50 overflow-y-auto"
    >
      {/* Header */}
      <div className="sticky top-0 bg-black bg-opacity-90 backdrop-blur-sm z-10 px-4 py-3 flex items-center justify-between">
        <button onClick={onClose} className="text-white">
          <i className="bi bi-arrow-left text-xl"></i>
        </button>
        <span className="text-white font-semibold">{user.username}</span>
        <button className="text-white">
          <i className="bi bi-three-dots text-xl"></i>
        </button>
      </div>

      {/* Profile Info */}
      <div className="px-4 py-6 text-white">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <img
              src={user.avatar}
              alt={user.displayName}
              className="w-24 h-24 rounded-full border-2 border-gray-600 mb-4"
              crossOrigin="anonymous"
            />
            <div className="flex items-center mb-2">
              <h1 className="text-2xl font-bold mr-2">{user.displayName}</h1>
              {user.verified && (
                <i className="bi bi-patch-check-fill text-blue-400"></i>
              )}
            </div>
            <p className="text-gray-400 mb-4">@{user.username}</p>
          </div>
          
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsFollowing(!isFollowing)}
            className={`px-6 py-2 rounded-lg font-semibold transition-colors ${
              isFollowing
                ? 'bg-gray-700 text-white border border-gray-600'
                : 'bg-primary-500 text-white'
            }`}
          >
            {isFollowing ? 'Following' : 'Follow'}
          </motion.button>
        </div>

        <p className="text-gray-300 mb-6 leading-relaxed">{user.bio}</p>

        {/* Stats */}
        <div className="flex space-x-6 mb-6">
          <div className="text-center">
            <div className="text-xl font-bold">{formatNumber(user.following)}</div>
            <div className="text-gray-400 text-sm">Following</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-bold">{formatNumber(user.followers)}</div>
            <div className="text-gray-400 text-sm">Followers</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-bold">{userVideos.length}</div>
            <div className="text-gray-400 text-sm">Videos</div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700 mb-6">
          <button
            onClick={() => setActiveTab('videos')}
            className={`flex-1 py-3 text-center font-semibold transition-colors ${
              activeTab === 'videos'
                ? 'text-white border-b-2 border-primary-500'
                : 'text-gray-400'
            }`}
          >
            <i className="bi bi-grid-3x3 mr-2"></i>
            Videos
          </button>
          <button
            onClick={() => setActiveTab('liked')}
            className={`flex-1 py-3 text-center font-semibold transition-colors ${
              activeTab === 'liked'
                ? 'text-white border-b-2 border-primary-500'
                : 'text-gray-400'
            }`}
          >
            <i className="bi bi-heart mr-2"></i>
            Liked
          </button>
        </div>

        {/* Video Grid */}
        <div className="grid grid-cols-3 gap-1">
          {activeTab === 'videos' ? (
            userVideos.map((video) => (
              <motion.div
                key={video.id}
                whileTap={{ scale: 0.95 }}
                className="aspect-[9/16] bg-gray-800 rounded-lg overflow-hidden relative cursor-pointer"
              >
                <img
                  src={video.thumbnail}
                  alt={video.caption}
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                />
                <div className="absolute bottom-2 left-2 text-white text-xs flex items-center">
                  <i className="bi bi-play-fill mr-1"></i>
                  {formatNumber(video.likes)}
                </div>
              </motion.div>
            ))
          ) : (
            <div className="col-span-3 text-center py-12 text-gray-400">
              <i className="bi bi-heart text-4xl mb-4 block"></i>
              <p>No liked videos yet</p>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default UserProfile;
