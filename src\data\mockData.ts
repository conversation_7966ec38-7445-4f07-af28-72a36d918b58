export interface User {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  bio: string;
  followers: number;
  following: number;
  verified: boolean;
  isFollowing?: boolean;
  createdAt?: string;
  location?: string;
}

export interface Comment {
  id: string;
  user: User;
  text: string;
  likes: number;
  replies: Comment[];
  createdAt: string;
  isLiked: boolean;
}

export interface Video {
  id: string;
  user: User;
  videoUrl: string;
  thumbnail: string;
  caption: string;
  likes: number;
  comments: number;
  shares: number;
  music: string;
  duration: number;
  isLiked: boolean;
  isBookmarked?: boolean;
  hashtags: string[];
  createdAt: string;
  views: number;
  commentsData?: Comment[];
}

export interface MusicTrack {
  id: string;
  name: string;
  artist: string;
  duration: number;
  coverUrl: string;
  audioUrl: string;
}

export const mockUsers: User[] = [
  {
    id: '1',
    username: 'sarah_creates',
    displayName: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Digital artist & content creator ✨ Sharing daily inspiration',
    followers: 125000,
    following: 892,
    verified: true,
    isFollowing: false,
    createdAt: '2023-01-15',
    location: 'Los Angeles, CA'
  },
  {
    id: '2',
    username: 'mike_fitness',
    displayName: 'Mike Rodriguez',
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Fitness coach 💪 Transform your life one workout at a time',
    followers: 89000,
    following: 456,
    verified: false,
    isFollowing: true,
    createdAt: '2023-03-22',
    location: 'Miami, FL'
  },
  {
    id: '3',
    username: 'chef_emma',
    displayName: 'Emma Chen',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Professional chef 👩‍🍳 Quick recipes for busy people',
    followers: 234000,
    following: 123,
    verified: true,
    isFollowing: false,
    createdAt: '2022-11-08',
    location: 'New York, NY'
  },
  {
    id: '4',
    username: 'travel_alex',
    displayName: 'Alex Thompson',
    avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'World traveler 🌍 Sharing hidden gems from around the globe',
    followers: 67000,
    following: 789,
    verified: false,
    isFollowing: true,
    createdAt: '2023-05-12',
    location: 'San Francisco, CA'
  },
  {
    id: '5',
    username: 'music_maya',
    displayName: 'Maya Patel',
    avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Singer-songwriter 🎵 Sharing original music and covers',
    followers: 156000,
    following: 234,
    verified: true,
    isFollowing: false,
    createdAt: '2023-02-28',
    location: 'Nashville, TN'
  },
  {
    id: '6',
    username: 'tech_tom',
    displayName: 'Tom Wilson',
    avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    bio: 'Tech reviewer 📱 Latest gadgets and tech tips',
    followers: 98000,
    following: 567,
    verified: false,
    isFollowing: false,
    createdAt: '2023-04-10',
    location: 'Seattle, WA'
  }
];

// Mock comments data
export const mockComments: Comment[] = [
  {
    id: '1',
    user: mockUsers[1],
    text: 'This is absolutely amazing! 🔥',
    likes: 45,
    replies: [],
    createdAt: '2024-01-15T10:30:00Z',
    isLiked: false
  },
  {
    id: '2',
    user: mockUsers[2],
    text: 'Love the creativity! How long did this take?',
    likes: 23,
    replies: [
      {
        id: '2-1',
        user: mockUsers[0],
        text: 'About 3 hours! Thanks for watching 😊',
        likes: 12,
        replies: [],
        createdAt: '2024-01-15T11:00:00Z',
        isLiked: true
      }
    ],
    createdAt: '2024-01-15T10:45:00Z',
    isLiked: true
  }
];

// Mock music tracks
export const mockMusicTracks: MusicTrack[] = [
  {
    id: '1',
    name: 'Chill Vibes',
    artist: 'Lo-fi Beats',
    duration: 180,
    coverUrl: 'https://images.pexels.com/photos/1763075/pexels-photo-1763075.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: '2',
    name: 'Pump It Up',
    artist: 'Workout Mix',
    duration: 210,
    coverUrl: 'https://images.pexels.com/photos/1763075/pexels-photo-1763075.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  }
];

export const mockVideos: Video[] = [
  {
    id: '1',
    user: mockUsers[0],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnail: 'https://images.pexels.com/photos/1181673/pexels-photo-1181673.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: 'Creating digital art magic ✨ What do you think of this piece?',
    likes: 12500,
    comments: 892,
    shares: 234,
    music: 'Chill Vibes - Lo-fi Beats',
    duration: 15,
    isLiked: false,
    isBookmarked: false,
    hashtags: ['#digitalart', '#creative', '#art'],
    createdAt: '2024-01-15T08:00:00Z',
    views: 45600,
    commentsData: mockComments
  },
  {
    id: '2',
    user: mockUsers[1],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    thumbnail: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: '30-second morning workout routine 💪 Try this every day for amazing results!',
    likes: 8900,
    comments: 456,
    shares: 123,
    music: 'Pump It Up - Workout Mix',
    duration: 30,
    isLiked: true,
    isBookmarked: true,
    hashtags: ['#fitness', '#workout', '#morning'],
    createdAt: '2024-01-14T06:30:00Z',
    views: 23400,
    commentsData: []
  },
  {
    id: '3',
    user: mockUsers[2],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    thumbnail: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: '5-minute pasta recipe that will blow your mind 🍝 Perfect for busy weeknights!',
    likes: 15600,
    comments: 1200,
    shares: 567,
    music: 'Kitchen Vibes - Upbeat',
    duration: 45,
    isLiked: false,
    isBookmarked: false,
    hashtags: ['#cooking', '#recipe', '#pasta', '#quickmeals'],
    createdAt: '2024-01-13T18:00:00Z',
    views: 67800,
    commentsData: []
  },
  {
    id: '4',
    user: mockUsers[3],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    thumbnail: 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: 'Hidden waterfall in Bali 🌊 This place is absolutely magical! Who wants the location?',
    likes: 23400,
    comments: 2100,
    shares: 890,
    music: 'Tropical Dreams - Ambient',
    duration: 25,
    isLiked: true,
    isBookmarked: true,
    hashtags: ['#travel', '#bali', '#waterfall', '#nature'],
    createdAt: '2024-01-12T14:20:00Z',
    views: 89200,
    commentsData: []
  },
  {
    id: '5',
    user: mockUsers[4],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    thumbnail: 'https://images.pexels.com/photos/1407322/pexels-photo-1407322.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: 'New original song! What do you think of the melody? 🎵',
    likes: 18700,
    comments: 934,
    shares: 445,
    music: 'Original - Maya Patel',
    duration: 35,
    isLiked: false,
    isBookmarked: false,
    hashtags: ['#music', '#original', '#singer', '#songwriter'],
    createdAt: '2024-01-11T20:15:00Z',
    views: 52300,
    commentsData: []
  },
  {
    id: '6',
    user: mockUsers[5],
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4',
    thumbnail: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400&h=600&fit=crop',
    caption: 'iPhone 15 Pro Max review - Is it worth the upgrade? 📱',
    likes: 9800,
    comments: 567,
    shares: 234,
    music: 'Tech Review Theme',
    duration: 60,
    isLiked: false,
    isBookmarked: false,
    hashtags: ['#tech', '#iphone', '#review', '#apple'],
    createdAt: '2024-01-10T16:45:00Z',
    views: 34500,
    commentsData: []
  }
];
