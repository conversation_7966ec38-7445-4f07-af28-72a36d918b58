import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Video } from '../data/mockData';

interface VideoCardProps {
  video: Video;
  isActive: boolean;
  onLike: (videoId: string) => void;
  onComment: (videoId: string) => void;
  onShare: (videoId: string) => void;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, isActive, onLike, onComment, onShare }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current) {
      if (isActive) {
        videoRef.current.play();
        setIsPlaying(true);
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  }, [isActive]);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        videoRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        loop
        muted
        playsInline
        poster={video.thumbnail}
        onClick={togglePlay}
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => window.setTimeout(() => setShowControls(false), 3000)}
      >
        <source src={video.videoUrl} type="video/mp4" />
      </video>

      {/* Play/Pause Overlay */}
      {!isPlaying && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30"
          onClick={togglePlay}
        >
          <div className="w-20 h-20 bg-white bg-opacity-80 rounded-full flex items-center justify-center">
            <i className="bi bi-play-fill text-3xl text-black ml-1"></i>
          </div>
        </motion.div>
      )}

      {/* User Info and Caption */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: showControls ? 1 : 0.7, y: 0 }}
        className="absolute bottom-20 left-4 right-20 text-white"
      >
        <div className="flex items-center mb-3">
          <img
            src={video.user.avatar}
            alt={video.user.displayName}
            className="w-12 h-12 rounded-full border-2 border-white mr-3"
            crossOrigin="anonymous"
          />
          <div>
            <div className="flex items-center">
              <span className="font-semibold text-lg">{video.user.username}</span>
              {video.user.verified && (
                <i className="bi bi-patch-check-fill text-blue-400 ml-1"></i>
              )}
            </div>
            <span className="text-sm text-gray-300">{video.user.displayName}</span>
          </div>
        </div>
        
        <p className="text-sm mb-2 leading-relaxed">{video.caption}</p>
        
        <div className="flex items-center text-xs text-gray-300">
          <i className="bi bi-music-note mr-1"></i>
          <span>{video.music}</span>
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: showControls ? 1 : 0.7, x: 0 }}
        className="absolute bottom-20 right-4 flex flex-col items-center space-y-6"
      >
        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => onLike(video.id)}
          className="flex flex-col items-center"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
            video.isLiked ? 'bg-primary-500' : 'bg-gray-800 bg-opacity-70'
          }`}>
            <i className={`bi ${video.isLiked ? 'bi-heart-fill' : 'bi-heart'} text-xl text-white`}></i>
          </div>
          <span className="text-xs text-white mt-1">{formatNumber(video.likes)}</span>
        </motion.button>

        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => onComment(video.id)}
          className="flex flex-col items-center"
        >
          <div className="w-12 h-12 bg-gray-800 bg-opacity-70 rounded-full flex items-center justify-center">
            <i className="bi bi-chat text-xl text-white"></i>
          </div>
          <span className="text-xs text-white mt-1">{formatNumber(video.comments)}</span>
        </motion.button>

        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => onShare(video.id)}
          className="flex flex-col items-center"
        >
          <div className="w-12 h-12 bg-gray-800 bg-opacity-70 rounded-full flex items-center justify-center">
            <i className="bi bi-share text-xl text-white"></i>
          </div>
          <span className="text-xs text-white mt-1">{formatNumber(video.shares)}</span>
        </motion.button>

        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 rounded-full border-2 border-white overflow-hidden"
        >
          <img
            src={video.user.avatar}
            alt="Music"
            className="w-full h-full object-cover"
            crossOrigin="anonymous"
          />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default VideoCard;
