import React from 'react';
import { motion } from 'framer-motion';

interface VideoControlsProps {
  isPlaying: boolean;
  isMuted: boolean;
  volume: number;
  onPlayPause: () => void;
  onMute: () => void;
  onVolumeChange: (volume: number) => void;
  onFullscreen: () => void;
}

const VideoControls: React.FC<VideoControlsProps> = ({
  isPlaying,
  isMuted,
  volume,
  onPlayPause,
  onMute,
  onVolumeChange,
  onFullscreen
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="absolute bottom-4 left-4 right-4 flex items-center justify-between bg-black bg-opacity-50 rounded-lg p-3 backdrop-blur-sm"
    >
      {/* Left Controls */}
      <div className="flex items-center space-x-3">
        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={onPlayPause}
          className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-colors"
        >
          <i className={`bi ${isPlaying ? 'bi-pause-fill' : 'bi-play-fill'} text-lg ${!isPlaying ? 'ml-0.5' : ''}`}></i>
        </motion.button>

        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={onMute}
          className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors"
        >
          <i className={`bi ${isMuted ? 'bi-volume-mute-fill' : volume > 0.5 ? 'bi-volume-up-fill' : 'bi-volume-down-fill'} text-lg`}></i>
        </motion.button>

        {/* Volume Slider */}
        <div className="hidden sm:flex items-center space-x-2">
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
            className="w-16 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
            style={{
              background: `linear-gradient(to right, #ffffff 0%, #ffffff ${(isMuted ? 0 : volume) * 100}%, #6b7280 ${(isMuted ? 0 : volume) * 100}%, #6b7280 100%)`
            }}
          />
        </div>
      </div>

      {/* Right Controls */}
      <div className="flex items-center space-x-3">
        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={onFullscreen}
          className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors"
        >
          <i className="bi bi-fullscreen text-lg"></i>
        </motion.button>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #ffffff;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider::-moz-range-thumb {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #ffffff;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </motion.div>
  );
};

export default VideoControls;
