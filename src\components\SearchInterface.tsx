import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { mockUsers, mockVideos } from '../data/mockData';

interface SearchInterfaceProps {
  onClose: () => void;
  onUserSelect: (user: any) => void;
}

const SearchInterface: React.FC<SearchInterfaceProps> = ({ onClose, onUserSelect }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'users' | 'videos' | 'sounds'>('users');

  const filteredUsers = mockUsers.filter(user =>
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.displayName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredVideos = mockVideos.filter(video =>
    video.caption.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const trendingHashtags = [
    '#fyp', '#viral', '#trending', '#dance', '#comedy', '#food', '#travel', '#art'
  ];

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black z-50 overflow-y-auto"
    >
      {/* Header */}
      <div className="sticky top-0 bg-black bg-opacity-90 backdrop-blur-sm z-10 px-4 py-3">
        <div className="flex items-center space-x-3">
          <button onClick={onClose} className="text-white">
            <i className="bi bi-arrow-left text-xl"></i>
          </button>
          <div className="flex-1 relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search users, videos, sounds..."
              className="w-full bg-gray-900 text-white rounded-full px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-primary-500"
              autoFocus
            />
            <i className="bi bi-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex mt-4 border-b border-gray-700">
          {['users', 'videos', 'sounds'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as any)}
              className={`flex-1 py-3 text-center font-semibold capitalize transition-colors ${
                activeTab === tab
                  ? 'text-white border-b-2 border-primary-500'
                  : 'text-gray-400'
              }`}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>

      <div className="px-4 py-4">
        {!searchQuery ? (
          /* Trending Content */
          <div>
            <h3 className="text-white font-semibold mb-4">Trending Hashtags</h3>
            <div className="space-y-3">
              {trendingHashtags.map((hashtag, index) => (
                <motion.div
                  key={hashtag}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-gray-900 rounded-lg"
                >
                  <div>
                    <div className="text-white font-semibold">{hashtag}</div>
                    <div className="text-gray-400 text-sm">
                      {formatNumber(Math.floor(Math.random() * 1000000) + 100000)} videos
                    </div>
                  </div>
                  <i className="bi bi-chevron-right text-gray-400"></i>
                </motion.div>
              ))}
            </div>
          </div>
        ) : (
          /* Search Results */
          <div>
            {activeTab === 'users' && (
              <div className="space-y-3">
                {filteredUsers.map((user) => (
                  <motion.div
                    key={user.id}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => onUserSelect(user)}
                    className="flex items-center p-3 bg-gray-900 rounded-lg cursor-pointer hover:bg-gray-800 transition-colors"
                  >
                    <img
                      src={user.avatar}
                      alt={user.displayName}
                      className="w-12 h-12 rounded-full mr-3"
                      crossOrigin="anonymous"
                    />
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-white font-semibold mr-1">{user.username}</span>
                        {user.verified && (
                          <i className="bi bi-patch-check-fill text-blue-400"></i>
                        )}
                      </div>
                      <div className="text-gray-400 text-sm">{user.displayName}</div>
                      <div className="text-gray-500 text-xs">
                        {formatNumber(user.followers)} followers
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'videos' && (
              <div className="grid grid-cols-3 gap-1">
                {filteredVideos.map((video) => (
                  <motion.div
                    key={video.id}
                    whileTap={{ scale: 0.95 }}
                    className="aspect-[9/16] bg-gray-800 rounded-lg overflow-hidden relative cursor-pointer"
                  >
                    <img
                      src={video.thumbnail}
                      alt={video.caption}
                      className="w-full h-full object-cover"
                      crossOrigin="anonymous"
                    />
                    <div className="absolute bottom-2 left-2 text-white text-xs flex items-center">
                      <i className="bi bi-play-fill mr-1"></i>
                      {formatNumber(video.likes)}
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'sounds' && (
              <div className="space-y-3">
                {['Chill Vibes - Lo-fi Beats', 'Pump It Up - Workout Mix', 'Kitchen Vibes - Upbeat'].map((sound, index) => (
                  <motion.div
                    key={sound}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center p-3 bg-gray-900 rounded-lg"
                  >
                    <div className="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center mr-3">
                      <i className="bi bi-music-note text-white"></i>
                    </div>
                    <div className="flex-1">
                      <div className="text-white font-semibold">{sound}</div>
                      <div className="text-gray-400 text-sm">
                        {formatNumber(Math.floor(Math.random() * 100000) + 10000)} videos
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default SearchInterface;
