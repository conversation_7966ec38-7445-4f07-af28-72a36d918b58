import { useState, useRef, useCallback, useEffect } from 'react';

export interface InfiniteScrollOptions {
  threshold?: number;
  rootMargin?: string;
}

export const useInfiniteScroll = <T>(
  initialItems: T[],
  loadMore: () => Promise<T[]>,
  options: InfiniteScrollOptions = {}
) => {
  const [items, setItems] = useState<T[]>(initialItems);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);

  const { threshold = 0.1, rootMargin = '100px' } = options;

  const handleLoadMore = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    setError(null);

    try {
      const newItems = await loadMore();
      
      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        setItems(prev => [...prev, ...newItems]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more items');
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, hasMore, loadMore]);

  // Set up intersection observer
  useEffect(() => {
    if (!loadingRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          handleLoadMore();
        }
      },
      { threshold, rootMargin }
    );

    observerRef.current.observe(loadingRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [handleLoadMore, threshold, rootMargin]);

  const reset = useCallback(() => {
    setItems(initialItems);
    setHasMore(true);
    setError(null);
    setIsLoading(false);
  }, [initialItems]);

  return {
    items,
    isLoading,
    hasMore,
    error,
    loadingRef,
    reset,
    loadMore: handleLoadMore
  };
};
