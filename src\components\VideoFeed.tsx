import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import VideoCard from './VideoCard';
import CommentsModal from './CommentsModal';
import { mockVideos, Video, Comment } from '../data/mockData';
import { useGestures } from '../hooks/useGestures';
import { useLocalStorage } from '../hooks/useLocalStorage';

const VideoFeed: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [videos, setVideos] = useState<Video[]>(mockVideos);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);

  // Persistent state
  const [likedVideos, setLikedVideos] = useLocalStorage<string[]>('likedVideos', []);
  const [bookmarkedVideos, setBookmarkedVideos] = useLocalStorage<string[]>('bookmarkedVideos', []);

  const containerRef = useRef<HTMLDivElement>(null);

  // Navigation functions
  const goToNext = useCallback(() => {
    if (!isScrolling && currentIndex < videos.length - 1) {
      setIsScrolling(true);
      setCurrentIndex(currentIndex + 1);
      setTimeout(() => setIsScrolling(false), 500);
    }
  }, [currentIndex, videos.length, isScrolling]);

  const goToPrevious = useCallback(() => {
    if (!isScrolling && currentIndex > 0) {
      setIsScrolling(true);
      setCurrentIndex(currentIndex - 1);
      setTimeout(() => setIsScrolling(false), 500);
    }
  }, [currentIndex, isScrolling]);

  // Gesture handling
  const gestureRef = useGestures({
    onSwipeUp: goToNext,
    onSwipeDown: goToPrevious
  });

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          goToPrevious();
          break;
        case 'ArrowDown':
          e.preventDefault();
          goToNext();
          break;
        case 'Escape':
          if (showComments) {
            setShowComments(false);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [goToNext, goToPrevious, showComments]);

  // Mouse wheel handling
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      if (e.deltaY > 0) {
        goToNext();
      } else {
        goToPrevious();
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
      return () => container.removeEventListener('wheel', handleWheel);
    }
  }, [goToNext, goToPrevious]);

  // Video interaction handlers
  const handleLike = useCallback((videoId: string) => {
    setVideos(prevVideos =>
      prevVideos.map(video => {
        if (video.id === videoId) {
          const isLiked = !video.isLiked;
          const newLikes = isLiked ? video.likes + 1 : video.likes - 1;

          // Update persistent storage
          if (isLiked) {
            setLikedVideos(prev => [...prev, videoId]);
          } else {
            setLikedVideos(prev => prev.filter(id => id !== videoId));
          }

          return { ...video, isLiked, likes: newLikes };
        }
        return video;
      })
    );
  }, [setLikedVideos]);

  const handleComment = useCallback((videoId: string) => {
    setSelectedVideoId(videoId);
    setShowComments(true);
  }, []);

  const handleShare = useCallback(async (videoId: string) => {
    const video = videos.find(v => v.id === videoId);
    if (!video) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: `Check out this video by ${video.user.username}`,
          text: video.caption,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        // You could show a toast notification here
        console.log('Link copied to clipboard');
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  }, [videos]);

  const handleBookmark = useCallback((videoId: string) => {
    setVideos(prevVideos =>
      prevVideos.map(video => {
        if (video.id === videoId) {
          const isBookmarked = !video.isBookmarked;

          // Update persistent storage
          if (isBookmarked) {
            setBookmarkedVideos(prev => [...prev, videoId]);
          } else {
            setBookmarkedVideos(prev => prev.filter(id => id !== videoId));
          }

          return { ...video, isBookmarked };
        }
        return video;
      })
    );
  }, [setBookmarkedVideos]);

  const handleUserClick = useCallback((userId: string) => {
    console.log('Navigate to user profile:', userId);
    // You would implement navigation to user profile here
  }, []);

  const handleAddComment = useCallback((videoId: string, text: string) => {
    // In a real app, this would make an API call
    console.log('Add comment:', { videoId, text });
    // For now, just close the modal
    setShowComments(false);
  }, []);

  const handleLikeComment = useCallback((commentId: string) => {
    console.log('Like comment:', commentId);
    // In a real app, this would make an API call
  }, []);

  // Initialize liked and bookmarked states from localStorage
  useEffect(() => {
    setVideos(prevVideos =>
      prevVideos.map(video => ({
        ...video,
        isLiked: likedVideos.includes(video.id),
        isBookmarked: bookmarkedVideos.includes(video.id)
      }))
    );
  }, [likedVideos, bookmarkedVideos]);

  const currentVideo = videos[currentIndex];

  return (
    <>
      <div
        ref={gestureRef}
        className="relative w-full h-screen overflow-hidden bg-black"
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '-100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="absolute inset-0"
          >
            <VideoCard
              video={currentVideo}
              isActive={true}
              onLike={handleLike}
              onComment={handleComment}
              onShare={handleShare}
              onBookmark={handleBookmark}
              onUserClick={handleUserClick}
            />
          </motion.div>
        </AnimatePresence>

        {/* Video Progress Indicators */}
        <div className="absolute top-4 right-4 flex flex-col space-y-1 z-10">
          {videos.map((_, index) => (
            <motion.div
              key={index}
              whileTap={{ scale: 0.8 }}
              onClick={() => !isScrolling && setCurrentIndex(index)}
              className={`w-1 h-8 rounded-full transition-all duration-300 cursor-pointer ${
                index === currentIndex ? 'bg-white' : 'bg-gray-500 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* Navigation Hints */}
        <AnimatePresence>
          {currentIndex === 0 && (
            <motion.div
              initial={{ opacity: 1 }}
              animate={{ opacity: 0 }}
              exit={{ opacity: 0 }}
              transition={{ delay: 3, duration: 1 }}
              className="absolute bottom-32 left-1/2 transform -translate-x-1/2 text-white text-center z-10 pointer-events-none"
            >
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <i className="bi bi-chevron-up text-2xl"></i>
              </motion.div>
              <p className="text-sm mt-1">Swipe up for more</p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Keyboard Shortcuts Hint */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 5, duration: 1 }}
          className="absolute top-4 left-4 text-white text-xs bg-black bg-opacity-50 rounded-lg p-2 z-10 hidden md:block"
        >
          <div>↑↓ Navigate</div>
          <div>Space Play/Pause</div>
          <div>Esc Close</div>
        </motion.div>
      </div>

      {/* Comments Modal */}
      <AnimatePresence>
        {showComments && selectedVideoId && (
          <CommentsModal
            video={videos.find(v => v.id === selectedVideoId)!}
            isOpen={showComments}
            onClose={() => setShowComments(false)}
            onAddComment={handleAddComment}
            onLikeComment={handleLikeComment}
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default VideoFeed;
